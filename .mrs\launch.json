{"version": "0.2.0", "configurations": [{"type": "mrs-debugger", "request": "launch", "name": "CH32V307VCT6_LCD", "cwd": "d:\\A沁恒开发\\工程文件\\智能门锁\\1.1\\CH32V307VCT6_LCD", "openOCDCfg": {"useLocalOpenOCD": true, "executable": "d:/A沁恒开发/MounRiver_Studio2/resources/app/resources/win32/components/WCH/OpenOCD/OpenOCD/bin/openocd.exe", "configOptions": ["-f \"d:/A沁恒开发/MounRiver_Studio2/resources/app/resources/win32/components/WCH/OpenOCD/OpenOCD/bin/wch-riscv.cfg\" -c \"chip_id CH32V30x\""], "gdbport": 3333, "telnetport": 4444, "tclport": 6666, "host": "localhost", "port": 3333, "skipDownloadBeforeDebug": false, "enablePageEraser": false, "enableNoZeroWaitingAreaFlash": false}, "gdbCfg": {"executable": "d:/A沁恒开发/MounRiver_Studio2/resources/app/resources/win32/components/WCH/Toolchain/RISC-V Embedded GCC/bin/riscv-none-embed-gdb.exe", "commands": ["set mem inaccessible-by-default off", "set architecture riscv:rv32", "set remotetimeout unlimited", "set disassembler-options xw"], "options": []}, "startup": {"initCommands": {"initReset": true, "initResetType": "init", "armSemihosting": false, "additionalCommands": []}, "loadedFiles": {"executableFile": "d:\\A沁恒开发\\工程文件\\智能门锁\\1.1\\CH32V307VCT6_LCD\\obj\\CH32V307VCT6_LCD.elf", "symbolFile": "d:\\A沁恒开发\\工程文件\\智能门锁\\1.1\\CH32V307VCT6_LCD\\obj\\CH32V307VCT6_LCD.elf", "executableFileOffset": 0, "symbolFileOffset": 0}, "runCommands": {"runReset": true, "runResetType": "halt", "additionalCommands": [], "setBreakAt": "handle_reset", "continue": true, "setProgramCounterAt": 0}, "debugInRAM": false}, "svdpath": "d:\\A沁恒开发\\MounRiver_Studio2\\resources\\app\\resources\\win32\\components\\WCH\\SDK\\default/RISC-V/CH32V307/NoneOS/CH32V307xx.svd", "output": {"showDebugGDBTrace": true, "saveDebugOutputToFile": false, "showDebugOutputTimestamps": true}, "isDualCoreDebug": false, "dualCoreDebugRole": null, "architecture": "RISC-V"}]}
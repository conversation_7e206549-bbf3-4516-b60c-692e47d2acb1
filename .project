<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<projectDescription>
  <name>CH32V307VCT6_LCD</name>
  <comment/>
  <projects/>
  <buildSpec>
    <buildCommand>
      <name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
      <triggers>clean,full,incremental,</triggers>
      <arguments/>
    </buildCommand>
    <buildCommand>
      <name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
      <triggers>full,incremental,</triggers>
      <arguments/>
    </buildCommand>
  </buildSpec>
  <natures>
    <nature>org.eclipse.cdt.core.cnature</nature>
    <nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
    <nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
  </natures>
  <linkedResources/>
  <filteredResources>
    <filter>
      <name/>
      <type>6</type>
      <matcher>
        <id>org.eclipse.ui.ide.multiFilter</id>
        <arguments>1.0-name-matches-false-false-*.wvproj</arguments>
      </matcher>
    </filter>
    <filter>
      <name>Driver</name>
      <type>6</type>
      <matcher>
        <id>org.eclipse.ui.ide.multiFilter</id>
        <arguments>1.0-name-matches-false-false-LCD.c</arguments>
      </matcher>
    </filter>
    <filter>
      <name>Driver</name>
      <type>6</type>
      <matcher>
        <id>org.eclipse.ui.ide.multiFilter</id>
        <arguments>1.0-name-matches-false-false-LCD.h</arguments>
      </matcher>
    </filter>
  </filteredResources>
</projectDescription>
# 气体传感器系统

## 项目介绍

本项目是一个完整的气体传感器检测系统，包含两个主要模块：

1. **气体传感器模块** (STC15F单片机) - 负责气体检测、ADC采样、温度补偿
2. **显示主板模块** (AT32F413单片机) - 负责数码管显示、红外遥控、通信处理

## 系统架构

### 气体传感器端 (STC15F)
- 支持多种气体类型检测（可燃气体、电化学气体、氧化性气体）
- 4通道ADC采样（气体信号、参考电压、温度传感器）
- 温度补偿算法
- 传感器校准和清零功能
- 传感器寿命管理
- UART通信协议

### 显示主板端 (AT32F413)
- 4位数码管显示（CH455驱动）
- 红外遥控操作
- 12级菜单系统
- Modbus RTU通信
- 4-20mA电流输出
- 继电器报警输出
- 参数Flash存储
- RT-Thread实时操作系统

## 主要功能

### 故障检测与指示
- **自动故障检测**：系统每1秒自动检测传感器连接状态
- **即时故障恢复**：一旦检测到传感器连接正常，故障灯立即熄灭
- **故障类型识别**：支持通信故障、传感器未找到、参数错误等多种故障类型

### 显示与操作
- 实时浓度显示（支持小数点显示）
- 红外遥控菜单操作
- 传感器类型设置
- 报警阈值配置
- 校准和清零功能

### 通信接口
- RS485/Modbus RTU通信
- 4-20mA模拟量输出
- 继电器开关量输出

## 最近更新

### v1.1 (2024-12-19)
- **修复故障灯控制问题**：故障灯现在能够在检测到传感器正常连接时立即熄灭，无需等待遥控器操作
- **优化故障检测机制**：在主循环中添加智能传感器状态检测（每1秒检测一次）
- **解决通信冲突问题**：避免主循环检测与菜单系统通信冲突，消除E2错误闪烁
- **修复氯气传感器显示问题**：解决氯气等氧化性气体（ID≥100）无法正常显示的问题
- **修复菜单返回问题**：解决进入设置界面后无法返回浓度显示界面的问题
- **实现同步响应**：故障灯与数码管显示同步变化，消除延迟现象
- **缩短启动时间**：将开机自检倒计时从180秒缩短至3秒，提升用户体验
- **改进用户体验**：故障恢复响应更加及时和直观，显示更加稳定

## 编译说明

### 气体传感器端
- 开发环境：Keil C51
- 目标芯片：STC15F系列
- 项目文件：`project/GasSensor.uvproj`

### 显示主板端
- 开发环境：Keil MDK-ARM
- 目标芯片：AT32F413
- 项目文件：`project/GasMonitor.uvprojx`
- 操作系统：RT-Thread

## 使用说明

### 硬件连接
1. 将气体传感器模块与显示主板通过RS485接口连接
2. 确保电源供电正常（24V）
3. 连接4-20mA输出到上位机或PLC

### 操作方法
1. **开机自检**：系统启动后会进行3秒倒计时自检
2. **正常显示**：自检完成后显示当前气体浓度值
3. **菜单操作**：按遥控器SET键进入菜单模式
4. **故障指示**：故障灯亮起表示传感器连接异常

### 菜单功能
- **F1**: 传感器类型设置
- **F2**: 量程查看
- **F3**: 传感器清零
- **F4**: 传感器校准
- **F5**: 低报警阈值设置
- **F6**: 高报警阈值设置
- **F7**: 通信地址设置
- **F8**: 保留功能
- **F9**: 系统自检
- **F10**: 电流输出校准
- **F11**: 通信波特率设置
- **F12**: 高报警继电器设置

## 故障排除

### 故障灯常亮
- **原因**：传感器未连接或通信故障
- **解决**：检查RS485连接线，确认传感器供电正常
- **说明**：系统会自动检测传感器状态，连接正常后故障灯自动熄灭

### 显示异常
- **E1**: 系统数据错误
- **E2**: 传感器未找到
- **E3**: 通信错误
- **E4**: 功能不支持
- **E5**: 传感器寿命到期
- **E6**: 参数非法

## 技术支持

如有技术问题，请联系技术支持团队。

## 版权说明

本项目遵循相应的开源协议，详情请参考LICENSE文件。

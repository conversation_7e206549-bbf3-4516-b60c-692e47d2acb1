#include "gpio.h"
#include "iic.h"
#include "ch455.h"
#include "menu.h"
#include "irreceive.h"
#include "usart.h"
#include "mb_slave.h"
#include "wdt.h"
#include "rs485.h"
#include "protocol.h" // 添加协议头文件以使用proto_read_sensor
#include "stdio.h"

static rt_thread_t wdtTaskHandler;
extern Rs485Handle *hrs485x;


static void wdt_thread(void *param)
{
    IoHandle_t run_ledx;
    io_init(&run_ledx, NORMAL_LED_PORT, NORMAL_LED_PIN, LOW_LEVEL_ACTIVE);
    wdt_init();
    while (1) {
        io_open(&run_ledx);
        rt_thread_delay(100);
        io_close(&run_ledx);
        rt_thread_delay(100);
        io_open(&run_ledx);
        rt_thread_delay(100);
        io_close(&run_ledx);
        rt_thread_delay(100);
        io_open(&run_ledx);
        rt_thread_delay(400);
        io_close(&run_ledx);
        rt_thread_delay(200);
        feed_wdt();
    }
}


int main(void)
{
    uint32_t eve;
    
    
    IoHandle_t fault_ledx;
    IoHandle_t high_ledx;
    IoHandle_t low_ledx;

    IoHandle_t high_relayx;
    IoHandle_t low_relayx;
    IoHandle_t volt_24v;

    
    io_init(&fault_ledx, FAULT_LED_PORT, FAULT_LED_PIN, LOW_LEVEL_ACTIVE);
    io_init(&high_ledx, HIGH_ALARM_LED_PORT, HIGH_ALARM_LED_PIN, LOW_LEVEL_ACTIVE);
    io_init(&low_ledx, LOW_ALARM_LED_PORT, LOW_ALARM_LED_PIN, LOW_LEVEL_ACTIVE);

    io_init(&high_relayx, HIGH_ALARM_RELAY_PORT, HIGH_ALARM_RELAY_PIN, HIGH_LEVEL_ACTIVE);
    io_init(&low_relayx, LOW_ALARM_RELAY_PORT, LOW_ALARM_RELAY_PIN, HIGH_LEVEL_ACTIVE);

    io_init(&volt_24v, VOL24V_OUTPUT_PORT_PORT, VOL24V_OUTPUT_PORT_PIN, HIGH_LEVEL_ACTIVE);

    
    iic_init();
    ch455_init();
    wdtTaskHandler = rt_thread_create("wdttask", wdt_thread, RT_NULL, 512, 10, 10);
    if (wdtTaskHandler != RT_NULL) {
        rt_thread_startup(wdtTaskHandler);
    }
    int rc = sys_para_load();
    if (rc == -1) {
        io_open(&fault_ledx);
        while (1) {
            rt_thread_delay(1000);
        }
    } else if (rc == -2) {
        rt_event_send(sysEvent, SYS_FAULT_EVENT);
        ch455_show_num(Ch455Num3, CLOSE_SEG);
        ch455_show_num(Ch455Num2, CLOSE_SEG);
        ch455_show_num(Ch455Num1, 0xE);
        ch455_show_num(Ch455Num0, 1);
        goto do_while;
    }
    
    ir_receive_init();
    uart_init();
    
    ch455_show_num(Ch455Num3, CLOSE_SEG);
		hrs485x = rs485_init(NULL, HIGH_LEVEL_TRANSMISSION);
		
    for (int i = 3; i >= 0; --i) { // 改为3秒倒计时
        ch455_show_num(Ch455Num2, CLOSE_SEG); // 百位不显示
        ch455_show_num(Ch455Num1, CLOSE_SEG); // 十位不显示
        ch455_show_num(Ch455Num0, i); // 只显示个位数
        rt_thread_delay(1000);
    }
    
    if (mb_slave_init() != 0) {
        rt_event_send(sysEvent, SYS_FAULT_EVENT);
        ch455_show_num(Ch455Num3, CLOSE_SEG);
        ch455_show_num(Ch455Num2, CLOSE_SEG);
        ch455_show_num(Ch455Num1, 0xE);
        ch455_show_num(Ch455Num0, 1);
        goto do_while;
    }
    
    if (menu_init() != 0) {
        rt_event_send(sysEvent, SYS_FAULT_EVENT);
        ch455_show_num(Ch455Num3, CLOSE_SEG);
        ch455_show_num(Ch455Num2, CLOSE_SEG);
        ch455_show_num(Ch455Num1, 0xE);
        ch455_show_num(Ch455Num0, 1);
    }
		
do_while:
    {
        static uint32_t sensor_check_count = 0; // 传感器检测计数器

        while(1) {

            if (rt_event_recv(sysEvent, SELF_CHECK_EVENT, RT_EVENT_FLAG_AND | RT_EVENT_FLAG_CLEAR, RT_WAITING_NO, &eve) == RT_EOK) {
                io_open(&low_ledx);
                io_open(&high_ledx);
                io_open(&fault_ledx);

                io_open(&low_relayx);
                io_open(&high_relayx);
                io_open(&volt_24v);
                rt_thread_delay(1000);

                io_close(&low_ledx);
                io_close(&high_ledx);
                io_close(&fault_ledx);

                io_close(&low_relayx);
                io_close(&high_relayx);
                io_close(&volt_24v);
                rt_thread_delay(100);
            }

            // 优化的故障恢复机制 - 每1秒检测一次，确保及时响应
            if (++sensor_check_count >= 100) { // 100 * 10ms = 1000ms
                sensor_check_count = 0;

                // 检查是否有传感器相关的故障事件
                uint32_t sensor_fault_events = SENSOR_UNFIND_EVENT | SENSOR_COMMU_EVENT;
                uint32_t current_events;

                if (rt_event_recv(sysEvent, sensor_fault_events, RT_EVENT_FLAG_OR, RT_WAITING_NO, &current_events) == RT_EOK) {
                    // 有传感器故障事件，尝试简单的连接测试
                    static sensor_info_t sensor_info;
                    int sensor_status = proto_read_sensor(&sensor_info);

                    if (sensor_status >= 0) {
                        // 传感器恢复正常，清除传感器相关的故障事件
                        rt_event_recv(sysEvent, sensor_fault_events, RT_EVENT_FLAG_OR | RT_EVENT_FLAG_CLEAR, RT_WAITING_NO, &current_events);
                    }
                }
            }

            if (rt_event_recv(sysEvent, 0x00FFFFFF, RT_EVENT_FLAG_OR, RT_WAITING_NO, &eve) == RT_EOK) {
                io_open(&fault_ledx);
                if (sysData.highAlarmRelay) {
                    io_close(&high_relayx);
                }
            } else {
                io_close(&fault_ledx);
                if (sysData.highAlarmRelay) {
                    io_open(&high_relayx);
                }
            }

            if (rt_event_recv(sysEvent, SENSOR_UNFIND_EVENT, RT_EVENT_FLAG_AND, RT_WAITING_NO, &eve) == RT_EOK) {
                io_open(&high_ledx);
                if (!sysData.highAlarmRelay) {
                    io_open(&high_relayx);
                }
            } else {
                io_close(&high_ledx);
                if (!sysData.highAlarmRelay) {
                    io_close(&high_relayx);
                }
            }

            if (rt_event_recv(sysEvent,  PARA_ILLEGA_EVENT, RT_EVENT_FLAG_AND, RT_WAITING_NO, &eve) == RT_EOK) {
                io_open(&high_ledx);
                if (!sysData.highAlarmRelay) {
                    io_open(&high_relayx);
                }
            } else {
                io_close(&high_ledx);
                if (!sysData.highAlarmRelay) {
                    io_close(&high_relayx);
                }
            }

            if (rt_event_recv(sysEvent, RELAY_HIGH_ALARM_EVENT, RT_EVENT_FLAG_AND, RT_WAITING_NO, &eve) == RT_EOK) {
                io_open(&high_ledx);
                if (!sysData.highAlarmRelay) {
                    io_open(&high_relayx);
                }
            } else {
                io_close(&high_ledx);
                if (!sysData.highAlarmRelay) {
                    io_close(&high_relayx);
                }
            }

            if (rt_event_recv(sysEvent, RELAY_LOW_ALARM_EVENT, RT_EVENT_FLAG_AND, RT_WAITING_NO, &eve) == RT_EOK) {
                io_open(&low_ledx);
                io_open(&low_relayx);
                io_open(&volt_24v);
            } else {
                io_close(&low_ledx);
                io_close(&low_relayx);
                io_close(&volt_24v);
            }
            rt_thread_delay(10);
        }
    }
}

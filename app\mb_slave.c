#include "rs485.h"
#include "sys.h"
#include "modbus.h"
#include <string.h>

static rt_mutex_t mbMutexId;
static rt_thread_t mbSlaveProcessThreadId;

modbus_map_t *regMap;
agile_modbus_t *ctx;
Rs485Handle *hrs485x;


void set_addr_from_ir(uint8_t addr) 
{
    rt_mutex_take(mbMutexId, RT_WAITING_FOREVER);
    regMap->holdingRegister[7] = addr;
    rt_mutex_release(mbMutexId);
}

void set_baudrate_from_ir(uint16_t baudrate) 
{
    rt_mutex_take(mbMutexId, RT_WAITING_FOREVER);
    regMap->holdingRegister[6] = baudrate;
    rt_mutex_release(mbMutexId);
}

static void set_slave_addr()
{
    if (regMap->holdingRegister[7] > 0 && regMap->holdingRegister[7] <= 240) {
        sysData.slaveAddr = regMap->holdingRegister[7];
        sync_to_flash(&sysData);
        agile_modbus_set_slave(ctx, sysData.slaveAddr);
    } else {
        regMap->holdingRegister[7] = sysData.slaveAddr;
    }
}


static void set_slave_baudrate()
{
    if (regMap->holdingRegister[6] == 9600 || regMap->holdingRegister[6] == 1200 || regMap->holdingRegister[6] == 2400
        || regMap->holdingRegister[6] == 4800 || regMap->holdingRegister[6] == 19200) {
            sysData.baudRate = regMap->holdingRegister[6];
            sync_to_flash(&sysData);
            Rs485ProtocolConfig_t cfg = {
                sysData.baudRate,
                USART_STOP_1_BIT,
                USART_PARITY_NONE
            };
            rs485_init(&cfg, HIGH_LEVEL_TRANSMISSION);
    } else {
        regMap->holdingRegister[6] = sysData.baudRate;
    }

}



static void mb_slave_process_thread(void *param)
{
//    uint8_t flag = 0;
    uint8_t ctx_send_buf[260];
	uint8_t ctx_read_buf[260];
	agile_modbus_rtu_t ctx_rtu;
    ctx = &ctx_rtu._ctx;
    agile_modbus_rtu_init(&ctx_rtu, ctx_send_buf, sizeof(ctx_send_buf), ctx_read_buf, sizeof(ctx_read_buf));
    agile_modbus_set_slave(ctx, sysData.slaveAddr);
    while (1)
    {
        if (sysData.baudRate != regMap->holdingRegister[6]) {
            set_slave_baudrate();
        }
        
        if (sysData.slaveAddr != regMap->holdingRegister[7]) {
            set_slave_addr();
        }
		int rc = rs485_read(hrs485x, ctx->read_buf, AGILE_MODBUS_MAX_ADU_LENGTH, 10);
        if (rc > 0) {
            rt_mutex_take(mbMutexId, RT_WAITING_FOREVER);
            int len = agile_modbus_slave_handle(ctx, rc, 1, agile_modbus_slave_util_callback, &slave_util, NULL);
			rt_mutex_release(mbMutexId);
            if (len > 0) {
				rs485_send(hrs485x, ctx->send_buf, len);
			}
        }
    }
}


int mb_slave_init(void)
{
    uart_monitor_time_init();
    //hrs485x = rs485_init(NULL, HIGH_LEVEL_TRANSMISSION);
    regMap = get_slave_mapping();
    regMap->holdingRegister[6] = sysData.baudRate;
    regMap->holdingRegister[7] = sysData.slaveAddr;
    mbMutexId = rt_mutex_create("mbmutex", RT_IPC_FLAG_PRIO);
    if (mbMutexId == RT_NULL) {
        return -1;
    }
    mbSlaveProcessThreadId = rt_thread_create("mbproc", mb_slave_process_thread, RT_NULL, 2048, 8, 10);
    if (mbSlaveProcessThreadId != RT_NULL) {
        rt_thread_startup(mbSlaveProcessThreadId);
    } else {
        return -1;
    }
    return 0;
}

#include "menu.h"
#include "ch455.h"
#include "irreceive.h"
#include "current.h"
#include "protocol.h"
#include "mb_slave.h"
#include "modbus.h"
#include "sys.h"


#define FIRST_LEVEL_TIMEOUT_COUNT             500
#define SECOND_LEVEL_TIMEOUT_COUNT            700
#define DETECTION_STEP                        1

static rt_thread_t menuTaskHandle;
extern modbus_map_t *regMap;

#define SUB_MENU_FCN_COUNT     12

#define show_success()      do {        \
                                    ch455_show_num(Ch455Num3, 5);   \
                                    ch455_show_num(Ch455Num2, 16);  \
                                    ch455_show_num(Ch455Num1, 12);  \
                                    ch455_show_num(Ch455Num0, 12);  \
                                } while (0)

static int func4_ret;

typedef void (*sub_menu_fcn)(void);


static void f1_menu_process(void);
static void f2_menu_process(void);
static void f3_menu_process(void);
static void f4_menu_process(void);
static void f5_menu_process(void);
static void f6_menu_process(void);
static void f7_menu_process(void);
static void f8_menu_process(void);
static void f9_menu_process(void);
static void f10_menu_process(void);
static void f11_menu_process(void);
static void fa_menu_process(void);


static const sub_menu_fcn subMenuFcnMap[] = {f1_menu_process, f2_menu_process, f3_menu_process, f4_menu_process,
                                            f5_menu_process, f6_menu_process, f7_menu_process, f8_menu_process, 
                                            f9_menu_process, f10_menu_process, f11_menu_process, fa_menu_process};


static void show_sensor_name(int sensor_id)
{
    switch (sensor_id) {
        case SENSOR_SO2: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 5);
            ch455_show_num(Ch455Num1, 0);
            ch455_show_num(Ch455Num0, 2);
        } break;
        case SENSOR_H2S: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xF + 2);
            ch455_show_num(Ch455Num1, 2);
            ch455_show_num(Ch455Num0, 5);
        } break;
        
        case SENSOR_CO: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, CLOSE_SEG);
            ch455_show_num(Ch455Num1, 0xC);
            ch455_show_num(Ch455Num0, 0);
        } break;
				case SENSOR_DEC: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xD);
            ch455_show_num(Ch455Num1, 0xE);
            ch455_show_num(Ch455Num0, 0xC);
        } break;
				case SENSOR_HCL: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xF + 2);
            ch455_show_num(Ch455Num1, 0xC);
            ch455_show_num(Ch455Num0, 0xF + 3);
        } break;
				case SENSOR_nH3: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xF + 6);
            ch455_show_num(Ch455Num1, 0xF + 2);
            ch455_show_num(Ch455Num0, 3);
        } break;
				case SENSOR_ETO: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xE);
            ch455_show_num(Ch455Num1, 7);
            ch455_show_num(Ch455Num0, 0);
        } break;
				case SENSOR_HCn: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xF + 2);
            ch455_show_num(Ch455Num1, 0xC);
            ch455_show_num(Ch455Num0, 0xF + 6);
        } break;
				case SENSOR_HCH: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xF + 2);
            ch455_show_num(Ch455Num1, 0xC);
            ch455_show_num(Ch455Num0, 0xF + 2);
        } break;
				case SENSOR_C8H: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xC);
            ch455_show_num(Ch455Num1, 8);
            ch455_show_num(Ch455Num0, 0xF + 2);
        } break;
				case SENSOR_3H4: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 3);
            ch455_show_num(Ch455Num1, 0xF + 2);
            ch455_show_num(Ch455Num0, 4);
        } break;
				case SENSOR_3H3: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 3);
            ch455_show_num(Ch455Num1, 0xF + 2);
            ch455_show_num(Ch455Num0, 3);
        } break;
				case SENSOR_4H6: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 4);
            ch455_show_num(Ch455Num1, 0xF + 2);
            ch455_show_num(Ch455Num0, 6);
        } break;
				case SENSOR_8H8: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 8);
            ch455_show_num(Ch455Num1, 0xF + 2);
            ch455_show_num(Ch455Num0, 8);
        } break;
				case SENSOR_H4O: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xF + 2);
            ch455_show_num(Ch455Num1, 4);
            ch455_show_num(Ch455Num0, 0);
        } break;
				case SENSOR_H6O: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xF + 2);
            ch455_show_num(Ch455Num1, 6);
            ch455_show_num(Ch455Num0, 0);
        } break;
				case SENSOR_3CL: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 3);
            ch455_show_num(Ch455Num1, 0xC);
            ch455_show_num(Ch455Num0, 0xF + 3);
        } break;
				case SENSOR_nO: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, CLOSE_SEG);
            ch455_show_num(Ch455Num1, 0xF + 6);
            ch455_show_num(Ch455Num0, 0);
        } break;
        case SENSOR_CH4: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xC);
            ch455_show_num(Ch455Num1, 0xF + 2);
            ch455_show_num(Ch455Num0, 4);
        } break;
        case SENSOR_C3H8: {
            ch455_show_num(Ch455Num3, 0xC);
            ch455_show_num(Ch455Num2, 3);
            ch455_show_num(Ch455Num1, 0xF + 2);
            ch455_show_num(Ch455Num0, 8);
        } break;
        case SENSOR_C4H10: {
            ch455_show_num(Ch455Num3, 0xC);
            ch455_show_num(Ch455Num2, 4);
            ch455_show_num(Ch455Num1, 0xF + 2);
            ch455_show_num(Ch455Num0, 1);
        } break;
        case SENSOR_H2: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, CLOSE_SEG);
            ch455_show_num(Ch455Num1, 0xF + 2);
            ch455_show_num(Ch455Num0, 2);
        } break;
        case SENSOR_O2: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, CLOSE_SEG);
            ch455_show_num(Ch455Num1, 0);
            ch455_show_num(Ch455Num0, 2);
        } break;
        case SENSOR_CL2: {
            ch455_show_num(Ch455Num3, CLOSE_SEG);
            ch455_show_num(Ch455Num2, 0xC);
            ch455_show_num(Ch455Num1, 0xF + 3);
            ch455_show_num(Ch455Num0, 2);
        } break;
    }
}


static void error_to_seg(int err_id)
{
    if (err_id < 0 && err_id > -10) {
        ch455_show_num(Ch455Num3, CLOSE_SEG);
        ch455_show_num(Ch455Num2, CLOSE_SEG);
        ch455_show_num(Ch455Num1, 0xe);
        ch455_show_num(Ch455Num0, -err_id);
        rt_event_send(sysEvent, MAKE_EVENT(err_id));
        regMap->holdingRegister[2] = -err_id;  
    } else {
        ch455_show_num(Ch455Num3, CLOSE_SEG);
        ch455_show_num(Ch455Num2, CLOSE_SEG);
        ch455_show_num(Ch455Num1, 0xe);
        ch455_show_num(Ch455Num0, 1);
        rt_event_send(sysEvent, MAKE_EVENT(-1));
        regMap->holdingRegister[2] = 1;  
    }
}


static void f1_menu_process(void)
{
    uint8_t key;
    uint16_t timeout = 0;
    int min = 0;
    int max = 0;
    int cur = proto_get_sensor_supports(&min, &max);
    if (cur <= 0) {
        error_to_seg(cur);
        rt_thread_delay(1000);
        return;
    } 
    while (1) {
        key = ir_key_scan();
        if (key == IR_KEY_RETURN) {
            return;
        } else if (key == IR_KEY_UP) {
            if (cur == max) {
                cur = min;
            } else {
                cur++;
            }
        } else if (key == IR_KEY_DOWN) {
            if (cur == min) {
                cur = max;
            } else {
                cur--;
            }
        } else if (key == IR_KEY_OK) {
            int ret = proto_set_sensor_type(cur);
            if (ret < 0) {
                error_to_seg(ret);
            } else {
                show_success();
            }
            rt_thread_delay(1500);
            return;  
        }
        show_sensor_name(cur);
        if (key != 0) {
            timeout = 0;
        }
        if (++timeout >= SECOND_LEVEL_TIMEOUT_COUNT) {
            return;
        }
        rt_thread_delay(10);
    }
}


static void f2_menu_process(void)
{
    uint8_t key;
    uint16_t timeout = 0;
    sensor_info_t sinfo;
    int ret = proto_read_sensor(&sinfo);
    if (ret < 0) {
        error_to_seg(ret);
        rt_thread_delay(1000);
        return;
    } else {
        ch455_show_num(Ch455Num3, sinfo.measureRange / 1000 > 0 ? 
                    sinfo.measureRange / 1000 : CLOSE_SEG);
        ch455_show_num(Ch455Num2, sinfo.measureRange / 100 > 0 ? 
                        sinfo.measureRange % 1000 / 100 : CLOSE_SEG);
        ch455_show_point_num(Ch455Num1, sinfo.measureRange % 100 / 10);
        ch455_show_num(Ch455Num0, sinfo.measureRange % 10);
    }
    while (1) {
        key = ir_key_scan();
        if (key == IR_KEY_RETURN) {
            return;
        } 
        if (key != 0) {
            timeout = 0;
        }
        if (++timeout >= SECOND_LEVEL_TIMEOUT_COUNT) {
            return;
        }
        rt_thread_delay(10);
    }
}


static void f3_menu_process(void)
{
    int ret = proto_clear_sensor();
    if (ret < 0) {
        error_to_seg(ret);
        rt_thread_delay(1000);
    } else {
        show_success();
        rt_thread_delay(3000);
    }
}


static void f4_menu_process(void)
{
    uint8_t key;
    uint16_t timeout = 0;
    uint16_t cali = 0;
    sensor_info_t sinfo;
    int ret = proto_read_sensor(&sinfo);
    if (ret < 0) {
        error_to_seg(ret);
        rt_thread_delay(1000);
        return;
    } else {
        cali = sinfo.measureRange / 2;
        ch455_show_num(Ch455Num3, cali / 1000);
        ch455_show_num(Ch455Num2, cali % 1000 / 100);
        ch455_show_point_num(Ch455Num1, cali % 100 / 10);
        ch455_show_num(Ch455Num0, cali % 10);
    }
    
    while (1) {
        key = ir_key_scan();
        if (key == IR_KEY_RETURN) {
            return;
        } else if (key == IR_KEY_DOWN) {
            cali -= 1;
        } else if (key == IR_KEY_UP) {
            cali += 1;
        } else if (key == IR_KEY_LEFT) {
            cali -= 10;
        } else if (key == IR_KEY_RIGHT) {
            cali += 10;
        } else if (key == IR_KEY_OK) {
            int rc = proto_calibrate_sensor(cali);
            if (rc >= 0) {
                show_success();
                func4_ret = 1;
            } else {
                error_to_seg(rc);
            }
            rt_thread_delay(3000);
            return;
        }
        
        if (cali < 0) {
            cali = 0;
        } else if (cali > sinfo.measureRange) {
            cali = sinfo.measureRange;
        }

        if (key != 0) {
            ch455_show_num(Ch455Num3, cali / 1000);
            ch455_show_num(Ch455Num2, cali % 1000 / 100);
            ch455_show_point_num(Ch455Num1, cali % 100 / 10);
            ch455_show_num(Ch455Num0, cali % 10);
            timeout = 0;
        }

        if (++timeout >= SECOND_LEVEL_TIMEOUT_COUNT) {
            return;
        }
        
        rt_thread_delay(10);
    }
}


static void f5_menu_process(void)
{
    uint8_t key;
    uint16_t timeout = 0;
    int low_alarm = 0;
    sensor_info_t sinfo;
    int ret = proto_read_sensor(&sinfo);
    if (ret < 0) {
        error_to_seg(ret);
        rt_thread_delay(1000);
        return;
    }
    
    low_alarm = sinfo.lowAlarm;
    ch455_show_num(Ch455Num3, low_alarm / 1000);
    ch455_show_num(Ch455Num2, low_alarm % 1000 / 100);
    ch455_show_point_num(Ch455Num1, low_alarm % 100 / 10);
    ch455_show_num(Ch455Num0, low_alarm % 10); 
    while (1) {
        key = ir_key_scan();
        if (key == IR_KEY_RETURN) {
            return;
        } else if (key == IR_KEY_DOWN) {
            low_alarm -= 1;
        } else if (key == IR_KEY_UP) {
            low_alarm += 1;
        } else if (key == IR_KEY_LEFT) {
            low_alarm -= 10;
        } else if (key == IR_KEY_RIGHT) {
            low_alarm += 10;
        } else if (key == IR_KEY_OK) {
            if (low_alarm > sinfo.highAlarm) {
                error_to_seg(-PARA_ILLEGA_ERROR);
            } else {
                ret = proto_set_sensor_alarm(low_alarm, sinfo.highAlarm);
                if (ret < 0) {
                    error_to_seg(ret);
                } else {
                    show_success(); 
                }  
            }
            rt_thread_delay(3000);
            return; 
        }
        
        if (low_alarm < 0) {
            low_alarm = 0;
        } else if (low_alarm > sinfo.highAlarm) {
            low_alarm -= sinfo.highAlarm;
        }

        if (key != 0) {
            ch455_show_num(Ch455Num3, low_alarm / 1000);
            ch455_show_num(Ch455Num2, low_alarm % 1000 / 100);
            ch455_show_point_num(Ch455Num1, low_alarm % 100 / 10);
            ch455_show_num(Ch455Num0, low_alarm % 10);
            timeout = 0;
        }

        if (++timeout >= SECOND_LEVEL_TIMEOUT_COUNT) {
            return;
        }
        
        rt_thread_delay(10);
    }
}


static void f6_menu_process(void)
{
    uint8_t key;
    uint16_t timeout = 0;
    int high_alarm = 0;
//    int range = 0;
    sensor_info_t sinfo;
    int ret = proto_read_sensor(&sinfo);
    if (ret < 0) {
        error_to_seg(ret);
        rt_thread_delay(1000);
        return;
    } 
    high_alarm = sinfo.highAlarm;
    ch455_show_num(Ch455Num3, high_alarm / 1000);
    ch455_show_num(Ch455Num2, high_alarm % 1000 / 100);
    ch455_show_point_num(Ch455Num1, high_alarm % 100 / 10);
    ch455_show_num(Ch455Num0, high_alarm % 10);
    while (1) {
        key = ir_key_scan();
        if (key == IR_KEY_RETURN) {
            return;
        } else if (key == IR_KEY_DOWN) {
            high_alarm -= 1;
        } else if (key == IR_KEY_UP) {
            high_alarm += 1;
        } else if (key == IR_KEY_LEFT) {
            high_alarm -= 10;
        } else if (key == IR_KEY_RIGHT) {
            high_alarm += 10;
        } else if (key == IR_KEY_OK) {
            if (high_alarm > sinfo.measureRange || 
                high_alarm <= sinfo.lowAlarm) {
                error_to_seg(-PARA_ILLEGA_ERROR);
            } else {
                ret = proto_set_sensor_alarm(sinfo.lowAlarm, high_alarm);
                if (ret < 0) {
                    error_to_seg(ret);
                } else {
                    show_success(); 
                } 
            }
            rt_thread_delay(3000);
            return; 
        }
        
        if (high_alarm < sinfo.lowAlarm) {
            high_alarm = sinfo.measureRange - (sinfo.lowAlarm - high_alarm);
        } else if (high_alarm > sinfo.measureRange) {
            high_alarm = high_alarm - sinfo.measureRange + sinfo.lowAlarm;
        }

        if (key != 0) {
            ch455_show_num(Ch455Num3, high_alarm / 1000);
            ch455_show_num(Ch455Num2, high_alarm % 1000 / 100);
            ch455_show_point_num(Ch455Num1, high_alarm % 100 / 10);
            ch455_show_num(Ch455Num0, high_alarm % 10);
            timeout = 0;
        }

        if (++timeout >= SECOND_LEVEL_TIMEOUT_COUNT) {
            return;
        }
        
        rt_thread_delay(10);
    }
}


static void f7_menu_process(void)
{
    uint8_t key;

//    uint8_t count = 0;
    uint16_t timeout = 0;
    uint8_t type = sysData.highAlarmRelay;
    ch455_show_num(Ch455Num2, CLOSE_SEG);
    ch455_show_num(Ch455Num1, CLOSE_SEG);
    ch455_show_num(Ch455Num0, 0xA);
    if (type) {
        ch455_show_num(Ch455Num3, 0xE);
    } else {
        ch455_show_num(Ch455Num3, 0xF + 2);
    }
    
    while (1) {
        key = ir_key_scan();
        if (key == IR_KEY_RETURN) {
            return;
        } else if (key == IR_KEY_DOWN || key == IR_KEY_UP) {
            type = !type;
            ch455_show_num(Ch455Num3, type ? 0xE: 0xF + 2);
        } else if (key == IR_KEY_OK) {
            sysData.highAlarmRelay = type;
            sync_to_flash(&sysData);
            show_success();
            rt_thread_delay(3000);
            return;
        }

        if (key != 0) {
            timeout = 0;
        }

        if (++timeout >= SECOND_LEVEL_TIMEOUT_COUNT) {
            return;
        }

        rt_thread_delay(10);
    }
}


static void f8_menu_process(void)
{
    // error_to_seg(-UNSUPPORTED_FUNC);
    // rt_thread_delay(1000);
}


static void f9_menu_process(void)
{
    rt_event_send(sysEvent, SELF_CHECK_EVENT);
    show_success();
    rt_thread_delay(1500);
}


static void f10_menu_process(void)
{

    uint8_t key;
    uint8_t state = 0;
    uint16_t timeout = 0;
    int current_seq[2];
    current_seq[0] = sysData.zeroDac;
    current_seq[1] = sysData.fullDac;

    ch455_show_num(Ch455Num3, current_seq[state]/1000);
    ch455_show_num(Ch455Num2, current_seq[state]%1000/100);
    ch455_show_num(Ch455Num1, current_seq[state]%100/10);
    ch455_show_num(Ch455Num0, current_seq[state]%10);
    set_dac_current(current_seq[state]);
    while (1)
    {
        key = ir_key_scan();
        if (key == IR_KEY_RETURN) {
            if (state) {
                state = !state;
            } else {
                return;
            }
        } else if (key == IR_KEY_DOWN) {
            current_seq[state]--;
        } else if (key == IR_KEY_UP) {
            current_seq[state]++;
        } else if (key == IR_KEY_LEFT) {
            current_seq[state] -= 10;
        } else if (key == IR_KEY_RIGHT) {
            current_seq[state] += 10;
        } else if (key == IR_KEY_OK) {
            if (state) {  
                if (current_seq[1] <= current_seq[0]) {
                    error_to_seg(-PARA_ILLEGA_ERROR);
                    rt_thread_delay(3000);
                    return;
                }
                sysData.fullDac = current_seq[1];
                sysData.zeroDac = current_seq[0];
                sync_to_flash(&sysData);
                show_success();
                rt_thread_delay(3000);
                return;
            } else {
                state = !state;
            }
        }

        if (current_seq[state] < 0) {
            current_seq[state] = 0;
        } else if (current_seq[state] > 0xFFF) {
            current_seq[state] = 0xFFF;
        } 

        if (key != 0) {
            ch455_show_num(Ch455Num3, current_seq[state]/1000);
            ch455_show_num(Ch455Num2, current_seq[state]%1000/100);
            ch455_show_num(Ch455Num1, current_seq[state]%100/10);
            ch455_show_num(Ch455Num0, current_seq[state]%10);
            set_dac_current(current_seq[state]);
            timeout = 0;
        }
        if (timeout++ >= SECOND_LEVEL_TIMEOUT_COUNT) {
            return;
        }
        
        rt_thread_delay(10);
    }

}


static void f11_menu_process(void)
{

    uint8_t key;
    uint16_t timeout = 0;
    int index;
    const uint16_t baud_rate_conf[5] = {1200, 2400, 4800, 9600, 19200};
    uint16_t baud_rate = sysData.baudRate;
    if (baud_rate > 9600) {
        index = 4;
    } else if (baud_rate > 4800) {
        index = 3;
    } else if (baud_rate > 2400) {
        index = 2;
    } else if (baud_rate > 1200) {
        index = 1;
    } else {
        index = 0;
    }
    while (1)
    {
        key = ir_key_scan(); 
        if (key == IR_KEY_DOWN) {
            index++;
        } else if (key == IR_KEY_UP) {
            index--;
        } else if (key == IR_KEY_OK) {
            set_baudrate_from_ir(baud_rate);
            show_success();
            rt_thread_delay(3000);
            return;
        } else if (key == IR_KEY_RETURN) {
            return;
        }
        if (index > 4) {
            index = 4;
        } else if (index < 0) {
            index = 0;
        }
        if (baud_rate_conf[index] > 10000) {
            baud_rate = baud_rate_conf[index];
            ch455_show_num(Ch455Num3, baud_rate/10000);
            ch455_show_num(Ch455Num2, baud_rate%10000/1000);
            ch455_show_num(Ch455Num1, baud_rate%1000/100);
            ch455_show_num(Ch455Num0, baud_rate%100/10);
        } else {
            baud_rate = baud_rate_conf[index];
            ch455_show_num(Ch455Num3, baud_rate/1000);
            ch455_show_num(Ch455Num2, baud_rate%1000/100);
            ch455_show_num(Ch455Num1, baud_rate%100/10);
            ch455_show_num(Ch455Num0, baud_rate%10);
        }
       
        if (key != 0) {
            timeout = 0;
        }
        if (timeout++ >= SECOND_LEVEL_TIMEOUT_COUNT) {
            return;
        }
        
        rt_thread_delay(10);     
    }
}


static void fa_menu_process(void)
{

    uint8_t key;
    uint16_t timeout = 0;
    int addr = sysData.slaveAddr;
    ch455_show_num(Ch455Num3, CLOSE_SEG);
    ch455_show_num(Ch455Num2, addr / 100);
    ch455_show_num(Ch455Num1, addr % 100 / 10);
    ch455_show_num(Ch455Num0, addr % 10);
    while (1)
    {
        key = ir_key_scan();
        if (key == IR_KEY_RETURN) {
           return;
        } else if (key == IR_KEY_OK) {
            if (addr > 240 || addr < 1) {
                error_to_seg(-PARA_ILLEGA_ERROR);
            } else {
                set_addr_from_ir(addr);
                show_success();      
            }
            rt_thread_delay(3000);
            return;
        } else if (key == IR_KEY_UP) {
            addr += 1;
        } else if (key == IR_KEY_DOWN) {
            addr -= 1;
        } else if (key == IR_KEY_LEFT) {
            addr -= 10;
        } else if (key == IR_KEY_RIGHT) {
            addr += 10;
        }
        if (addr > 240) {
            addr = 240;
        } else if (addr < 1) {
            addr = 1;
        }
        if (key != 0) {
            ch455_show_num(Ch455Num2, addr / 100);
            ch455_show_num(Ch455Num1, addr % 100 / 10);
            ch455_show_num(Ch455Num0, addr % 10);
            timeout = 0;
        }
       
        if (timeout++ >= SECOND_LEVEL_TIMEOUT_COUNT) {
            return;
        }
        rt_thread_delay(10);
    }
}


void set_menu(void)
{
    uint8_t key;
    uint8_t menu_index = 0;
    uint16_t timeout = 0;
    sub_menu_fcn menu_callback = subMenuFcnMap[menu_index];
    uint32_t eve;
    rt_event_control(sysEvent, RT_IPC_CMD_RESET, RT_NULL);
    ch455_show_num(Ch455Num3, CLOSE_SEG);
    ch455_show_num(Ch455Num2, CLOSE_SEG);
    ch455_show_num(Ch455Num1, 15);
    ch455_show_num(Ch455Num0, menu_index+1);
    while (1) {
        key = ir_key_scan();
        if (key == IR_KEY_RETURN) {
            return;
        } else if (key == IR_KEY_OK) {
            menu_callback();
            rt_event_recv(sysEvent, 0x00FFFFFE, RT_EVENT_FLAG_OR | RT_EVENT_FLAG_CLEAR, RT_WAITING_NO, (uint32_t *)&eve);
            if (func4_ret) {
                func4_ret = 0;
                return;
            }
        } else if (key == IR_KEY_UP) {
            if (menu_index > 0) {
                menu_index--;
            } else {
                menu_index = SUB_MENU_FCN_COUNT - 1;
            }
            menu_callback = subMenuFcnMap[menu_index];
        } else if (key == IR_KEY_DOWN) {

            if (menu_index < SUB_MENU_FCN_COUNT - 1) {
                menu_index++;
            } else {
                menu_index = 0;
            }
            menu_callback = subMenuFcnMap[menu_index];
        }

        if (key != 0) {
            timeout = 0;
        } else {
            if (timeout++ >= FIRST_LEVEL_TIMEOUT_COUNT) {
                return;
            }
        }
        ch455_show_num(Ch455Num3, CLOSE_SEG);
        
        if (menu_index < 9) {
            ch455_show_num(Ch455Num2, CLOSE_SEG);
            ch455_show_num(Ch455Num1, 15);
            ch455_show_num(Ch455Num0, menu_index + 1); 
        } else if (menu_index < 11){
            ch455_show_num(Ch455Num2, 15);
            ch455_show_num(Ch455Num1, (menu_index + 1) / 10);
            ch455_show_num(Ch455Num0, (menu_index + 1) % 10);
        } else {
            ch455_show_num(Ch455Num2, CLOSE_SEG);
            ch455_show_num(Ch455Num1, 15);
            ch455_show_num(Ch455Num0, 10);
        }
        rt_thread_delay(10);
    }
}


static void disp_menu()
{
    uint8_t key;
    uint8_t count = 10;
    uint8_t low_alarm = 0;
    uint8_t high_alarm = 0;
    uint32_t eve;
    sensor_info_t sinfo;
    while (1) {
        key = ir_key_scan();
        if (key == IR_KEY_SET) {
					return;
        }
        int rc = proto_read_sensor(&sinfo);
        if (rc < 0) {
					error_to_seg(rc);
            rt_thread_delay(100);
            continue;
        } else {
            // 传感器读取成功，立即清除传感器相关的故障事件
            uint32_t sensor_fault_events = SENSOR_UNFIND_EVENT | SENSOR_COMMU_EVENT;
            uint32_t current_events;
            rt_event_recv(sysEvent, sensor_fault_events, RT_EVENT_FLAG_OR | RT_EVENT_FLAG_CLEAR, RT_WAITING_NO, &current_events);
        }
        if (sinfo.sensorType == SENSOR_O2) {
            if (sinfo.realVal <= 210 && sinfo.realVal >= 208) {
                sinfo.realVal = 209;
            }
        } else if (sinfo.sensorType <= SENSOR_H2) {
            if (sinfo.realVal < 30) {
                sinfo.realVal = 0;
            }          
        }
        
        if (++count > 10) {
            count = 0;
            // 修复氯气等氧化性气体的显示问题 - 支持所有有效的传感器类型 (1-159)
            if (sinfo.sensorType > 0 && sinfo.sensorType <= 159) {
                if (sinfo.realVal > sinfo.measureRange) {
                    ch455_show_num(Ch455Num3, MID_LINE);
                    ch455_show_num(Ch455Num2, MID_LINE);
                    ch455_show_num(Ch455Num1, MID_LINE);
                    ch455_show_num(Ch455Num0, MID_LINE);
                } else {
                    ch455_show_num(Ch455Num3, sinfo.realVal/1000 > 0 ? sinfo.realVal%10000/1000 : CLOSE_SEG);
                    ch455_show_num(Ch455Num2, sinfo.realVal/100 > 0 ? sinfo.realVal%1000/100 : CLOSE_SEG);
                    ch455_show_point_num(Ch455Num1, sinfo.realVal%100/10);
                    ch455_show_num(Ch455Num0, sinfo.realVal%10);
                }
            }
        }
        set_current(sinfo.realVal, sinfo.measureRange);
        
        if (sinfo.realVal >= sinfo.highAlarm) {
            rt_event_send(sysEvent, RELAY_HIGH_ALARM_EVENT);
            high_alarm = DETECTION_STEP;   
        }
        
        if (high_alarm > 0) {
            high_alarm--;
        } else {
            rt_event_recv(sysEvent, RELAY_HIGH_ALARM_EVENT, 
                        RT_EVENT_FLAG_AND | RT_EVENT_FLAG_CLEAR, RT_WAITING_NO, &eve);
        }
        
        if (sinfo.sensorType == SENSOR_O2) {
            if (sinfo.realVal < sinfo.lowAlarm) {
                rt_event_send(sysEvent, RELAY_LOW_ALARM_EVENT);
                low_alarm = DETECTION_STEP;
            }
        } else {
            if (sinfo.realVal >= sinfo.lowAlarm) {
                rt_event_send(sysEvent, RELAY_LOW_ALARM_EVENT);
                low_alarm = DETECTION_STEP;
            }
        }
        
        if (low_alarm > 0) {
            low_alarm--;
        } else {
            rt_event_recv(sysEvent, RELAY_LOW_ALARM_EVENT, 
                        RT_EVENT_FLAG_AND | RT_EVENT_FLAG_CLEAR, RT_WAITING_NO, &eve);
        }
        regMap->holdingRegister[0] = sinfo.sensorType << 8 | sinfo.subSensorType;
        regMap->holdingRegister[1] = sinfo.realVal;
        regMap->holdingRegister[2] = 0;
        regMap->holdingRegister[3] = sinfo.measureRange;
        regMap->holdingRegister[4] = sinfo.lowAlarm;
        regMap->holdingRegister[5] = sinfo.highAlarm;
        rt_thread_delay(100);
    }
}


static void main_menu(void *arg)
{   

    while (1) {
        disp_menu();
        set_menu();
    }
}


int32_t menu_init()
{
    menuTaskHandle = rt_thread_create("menuTask", main_menu, RT_NULL, 2048, 8, 10);
    if (menuTaskHandle != RT_NULL) {
        rt_thread_startup(menuTaskHandle);
    }
    return menuTaskHandle == RT_NULL ? -1 : 0;
}

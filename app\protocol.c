#include "protocol.h"
#include <string.h>
#include "usart.h"


#define PROTO_CRC_ERROR             0xF1
#define PROTO_FUNC_ERROR            0xF2
#define PROTO_PARA_ERROR            0xF3
#define PROTO_NO_LIFE               0xF4

#define PROTO_GET_SENSOR_SUPP       0x01
#define PROTO_SET_SENSOR_TYPE       0x02
#define PROTO_READ_SENSOR_VAL       0x03
#define PROTO_CLEAR_SENSOR          0x04
#define PROTO_CALIBRATE_SENSOR      0x05
#define PROTO_SET_ALARM_VALUE       0x06


#define PROTO_SEND_LEN              8
#define PROTO_RECV_LEN              12
#define PROTO_READ_RETRY            3
#define PROTO_READ_TIMEOUT          300

static uint8_t comm_send_buf[PROTO_SEND_LEN];
static uint8_t comm_recv_buf[PROTO_RECV_LEN];


static int proto_send_recv(uint8_t *buf, uint8_t *out, int retry, uint16_t timeout)
{
    int rc = 0;
    buf[PROTO_SEND_LEN - 1] = 0;
    for (int i = 0; i < PROTO_SEND_LEN - 1; ++i) {
        buf[PROTO_SEND_LEN - 1] += buf[i];
    }
    for (int i = 0; i < retry; ++i) {
        uart_send(buf, PROTO_SEND_LEN);
        rc = uart_read(out, PROTO_RECV_LEN, timeout);
        if (rc > 0) {
            break;
        }
    }
    if (rc == 0) {
        return -SENSOR_UNFIND_ERROR;
    }
    if (rc != PROTO_RECV_LEN) {
        return -SENSOR_COMMU_ERROR;
    }
    uint8_t checksum = 0;
    for (int i = 0; i < PROTO_RECV_LEN - 1; ++i) {
        checksum += out[i];
    }
    if (checksum != out[PROTO_RECV_LEN - 1]) {
        return -SENSOR_COMMU_ERROR;
    }
    if (out[0] == PROTO_NO_LIFE) {
        return -SENSOR_NOLIFE_ERROR;
    } else if (out[0] == PROTO_FUNC_ERROR) {
        return -UNSUPPORTED_FUNC;
    } else if (out[0] == PROTO_PARA_ERROR) {
        return -PARA_ILLEGA_ERROR;
    }
    return 0;
}


int proto_set_sensor_type(int type)
{
    memset(comm_send_buf, 0, PROTO_SEND_LEN);
    comm_send_buf[0] = PROTO_SET_SENSOR_TYPE;
    comm_send_buf[3] = type;
    int rc = proto_send_recv(comm_send_buf, comm_recv_buf, PROTO_READ_RETRY, PROTO_READ_TIMEOUT);
    if (rc < 0) {
        return rc;
    }
    return 0;
}


int proto_clear_sensor(void)
{
    memset(comm_send_buf, 0, PROTO_SEND_LEN);
    comm_send_buf[0] = PROTO_CLEAR_SENSOR;
    int rc = proto_send_recv(comm_send_buf, comm_recv_buf, PROTO_READ_RETRY, PROTO_READ_TIMEOUT);
    if (rc < 0) {
        return rc;
    }
    return 0;
}


int proto_read_sensor(sensor_info_t *info)
{
    memset(comm_send_buf, 0, PROTO_SEND_LEN);
    comm_send_buf[0] = PROTO_READ_SENSOR_VAL;
    int rc = proto_send_recv(comm_send_buf, comm_recv_buf, PROTO_READ_RETRY, PROTO_READ_TIMEOUT);
    if (rc < 0) {
        return rc;
    }
    memcpy(info, &comm_recv_buf[1], sizeof(sensor_info_t));
    return 0;
}


int proto_calibrate_sensor(uint16_t cali)
{
    memset(comm_send_buf, 0, PROTO_SEND_LEN);
    comm_send_buf[0] = PROTO_CALIBRATE_SENSOR;
    memcpy(&comm_send_buf[3], &cali, sizeof(uint16_t));
    int rc = proto_send_recv(comm_send_buf, comm_recv_buf, PROTO_READ_RETRY, PROTO_READ_TIMEOUT);
    if (rc < 0) {
        return rc;
    }
    return 0;
}


int proto_get_sensor_supports(int *min, int *max)
{
    memset(comm_send_buf, 0, PROTO_SEND_LEN);
    comm_send_buf[0] = PROTO_GET_SENSOR_SUPP;
    int rc = proto_send_recv(comm_send_buf, comm_recv_buf, PROTO_READ_RETRY, PROTO_READ_TIMEOUT);
    if (rc < 0) {
        return rc;
    }
    *min = comm_recv_buf[3];
    *max = comm_recv_buf[4];
    return comm_recv_buf[1];
}

int proto_set_sensor_alarm(int low, int high)
{
    memset(comm_send_buf, 0, PROTO_SEND_LEN);
    comm_send_buf[0] = PROTO_SET_ALARM_VALUE;
    comm_send_buf[3] = low & 0xff;
    comm_send_buf[4] = low >> 8;
    comm_send_buf[5] = high & 0xff;
    comm_send_buf[6] = high >> 8;
    int rc = proto_send_recv(comm_send_buf, comm_recv_buf, PROTO_READ_RETRY, PROTO_READ_TIMEOUT);
    if (rc < 0) {
        return rc;
    }
    return 0;
}

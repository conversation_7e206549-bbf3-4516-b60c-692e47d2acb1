#ifndef __PROTOCOL_H_
#define __PROTOCOL_H_


#include "sys.h"



typedef struct {
    uint8_t sensorType;
    uint8_t subSensorType;
    uint16_t realVal;
    uint16_t lowAlarm;
    uint16_t highAlarm;
    uint16_t measureRange;
} sensor_info_t;

int proto_read_sensor(sensor_info_t *info);
int proto_clear_sensor(void);
int proto_set_sensor_type(int type);
int proto_calibrate_sensor(uint16_t cali);
int proto_get_sensor_supports(int *min, int *max);
int proto_set_sensor_alarm(int low, int high);
#endif

#include "sys.h"
#include "flash.h"
#include <string.h>
#include "rthw.h"

#define SAVE_ADDR       0x0800FC00

SysCoreData_t sysData;
rt_event_t sysEvent;


void rt_hw_us_delay(rt_uint32_t us)
{
    rt_uint32_t start, now, delta, reload, us_tick;
    start = SysTick->VAL;
    reload = SysTick->LOAD;
    us_tick = SystemCoreClock / 1000000UL;
    do {
        now = SysTick->VAL;
        delta = start > now ? start - now : reload + start - now;
    } while(delta < us_tick * us);
}


void sync_to_flash(SysCoreData_t *sysd)
{
    if (sysd == RT_NULL) {
        return;
    }
    rt_base_t level = rt_hw_interrupt_disable();
    flash_write(SAVE_ADDR, (uint16_t *)sysd, sizeof(SysCoreData_t) / 2);
    rt_hw_interrupt_enable(level);
}


void sync_from_flash(SysCoreData_t *sysd)
{
    if (sysd == RT_NULL) {
        return;
    }
    flash_read(SAVE_ADDR, (uint16_t *)sysd, sizeof(SysCoreData_t) / 2);
}


int sys_para_load(void)
{
    sysEvent = rt_event_create("sysEvent", RT_IPC_FLAG_PRIO);
    if (sysEvent == RT_NULL) {
        return -1;
    }
    sync_from_flash(&sysData);
    if (sysData.saveFlag != 0xCC) {
        memset(&sysData, 0, sizeof(sysData));
        sysData.saveFlag = 0xCC;
        sysData.slaveAddr = 1;
        sysData.highAlarmRelay = 0;
        sysData.baudRate = 9600;
        sysData.zeroDac = 663;
        sysData.fullDac = 3276;
        sync_to_flash(&sysData);
        return -2;
    }
    if (sysData.slaveAddr < 1 || sysData.slaveAddr > 240) {
        sysData.slaveAddr = 1;
    }
    if (sysData.baudRate != 9600 && sysData.baudRate != 1200 && sysData.baudRate != 2400
        && sysData.baudRate != 4800 && sysData.baudRate != 19200) {
            sysData.baudRate = 9600;
    }
    return 0;
}

#include "ch455.h"
#include "iic.h"



const uint8_t bcd_decode_tab[] = { 0X3F, 0X06, 0X5B, 0X4F, 0X66, 0X6D, 0X7D, 0X07, 0X7F, 0X6F, 
									 0X77, 0X7C, 0X39, 0X5E, 0X79, 0X71, 0x3E, 0x76, 0x38, 0X00, 0x40, 0x54};
// 10: A B C D E F U H L n

static inline void ch455_write(uint16_t data)	
{
    uint8_t cmd = ((uint8_t)(data>>7) & CH455_I2C_MASK) | CH455_I2C_ADDR;
    iic_start();
    iic_send_byte(cmd);
    iic_ack();
    iic_send_byte((uint8_t)data);
    iic_ack();
    iic_stop();
}

void ch455_show_num(Ch455Numx dig, uint8_t num)
{
   
    switch (dig) {
        
        case Ch455Num0: ch455_write(CH455_DIG3 | bcd_decode_tab[num]); break;
        case Ch455Num1: ch455_write(CH455_DIG2 | bcd_decode_tab[num]); break;
        case Ch455Num2: ch455_write(CH455_DIG1 | bcd_decode_tab[num]); break;
        case Ch455Num3: ch455_write(CH455_DIG0 | bcd_decode_tab[num]); break;      
    } 
}


void ch455_show_point_num(Ch455Numx dig, uint8_t num)
{
   
    switch (dig) {
        
        
        case Ch455Num0: ch455_write(CH455_DIG3_F | bcd_decode_tab[num]); break;
        case Ch455Num1: ch455_write(CH455_DIG2_F | bcd_decode_tab[num]); break;
        case Ch455Num2: ch455_write(CH455_DIG1_F | bcd_decode_tab[num]); break;
        case Ch455Num3: ch455_write(CH455_DIG0_F | bcd_decode_tab[num]); break;     
        
    } 
}

void ch455_init(void)
{
    ch455_show_num(Ch455Num0, CLOSE_SEG);
    ch455_show_num(Ch455Num1, CLOSE_SEG);
    ch455_show_num(Ch455Num2, CLOSE_SEG);
    ch455_show_num(Ch455Num3, CLOSE_SEG);
    ch455_write(CH455_SYSON | CH455_BIT_INTENS4); 
}


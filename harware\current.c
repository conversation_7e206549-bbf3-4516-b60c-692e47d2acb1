#include "current.h"
#include "iic.h"


void set_dac_current(uint16_t dac)
{
	iic_start();
	iic_send_byte(0xb0);
	iic_ack();
	iic_send_byte(0x02);
	iic_ack();
	iic_send_byte(dac << 4);
	iic_ack();
	iic_send_byte(dac >> 4);
	iic_ack();
	iic_stop();
}


void set_current(uint16_t dval, uint16_t mrange)
{
	if (dval > mrange) {
		set_dac_current(3604);
		return;
	}
	float temp = dval / (float)mrange * 
				(float)(sysData.fullDac - sysData.zeroDac) + sysData.zeroDac;
	set_dac_current(temp);
}

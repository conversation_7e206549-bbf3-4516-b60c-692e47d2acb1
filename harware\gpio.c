#include "gpio.h"


void io_close(IoHandle_t *hiox)
{
    
    if (hiox->active_bit == LOW_LEVEL_ACTIVE) {
        
        gpio_bits_set(hiox->gpiox, hiox->gpio_pins);
    } else {
        
        gpio_bits_reset(hiox->gpiox, hiox->gpio_pins);
    }
}



void io_open(IoHandle_t *hiox)
{
    
    if (hiox->active_bit == LOW_LEVEL_ACTIVE) {
        
        gpio_bits_reset(hiox->gpiox, hiox->gpio_pins);
    } else {
        
        gpio_bits_set(hiox->gpiox, hiox->gpio_pins);
    }
    
}



void io_init(IoHandle_t *hiox, gpio_type *gpiox, uint16_t gpio_pins, uint8_t activeBit)
{
    gpio_init_type gpio_init_struct;
    
    
    switch ((uint32_t)gpiox) {
        
        case (uint32_t)GPIOA: crm_periph_clock_enable(CRM_GPIOA_PERIPH_CLOCK, TRUE); break;
        case (uint32_t)GPIOB: crm_periph_clock_enable(CRM_GPIOB_PERIPH_CLOCK, TRUE); break;
        case (uint32_t)GPIOC: crm_periph_clock_enable(CRM_GPIOC_PERIPH_CLOCK, TRUE); break;
        case (uint32_t)GPIOD: crm_periph_clock_enable(CRM_GPIOD_PERIPH_CLOCK, TRUE); break;
    }
    
    gpio_default_para_init(&gpio_init_struct);
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
    gpio_init_struct.gpio_out_type  = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_OUTPUT;
    gpio_init_struct.gpio_pins = gpio_pins;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;

    gpio_init(gpiox, &gpio_init_struct);
    hiox->active_bit = activeBit;
    hiox->gpiox = gpiox;
    hiox->gpio_pins = gpio_pins;
    
    io_close(hiox); 
}




#include "iic.h"
#include "sys.h"
#include "rthw.h"

#define delay_us 		rt_hw_us_delay


void iic_start(void)
{
	SDA_OUT();     
	IIC_SDA = 1;	  	  
	IIC_SCL = 1;
	delay_us(4);
 	IIC_SDA = 0;
	delay_us(4);
	IIC_SCL = 0;
}


void iic_stop(void)
{
	SDA_OUT();
	IIC_SCL = 0;
	IIC_SDA = 0;
 	delay_us(4);
	IIC_SCL = 1; 
	IIC_SDA = 1;
	delay_us(4);							   	
}


unsigned char iic_wait_ack(void)
{
	unsigned char ucErrTime=0;
	SDA_IN();      
	IIC_SDA = 1;
	delay_us(1);	   
	IIC_SCL = 1;
	delay_us(1);	 
	while(READ_SDA)
	{
		ucErrTime++;
		if(ucErrTime > 250)
		{
			iic_stop();
			return 1;
		}
	}
	IIC_SCL = 0;
	return 0;  
} 



void iic_ack(void)
{
	IIC_SCL = 0;
	SDA_OUT();
	IIC_SDA = 0;
	delay_us(2);
	IIC_SCL = 1;
	delay_us(2);
	IIC_SCL = 0;
}


	    
void iic_nack(void)
{
	IIC_SCL=0;
	SDA_OUT();
	IIC_SDA=1;
	delay_us(2);
	IIC_SCL=1;
	delay_us(2);
	IIC_SCL=0;
}					 				     



void iic_send_byte(unsigned char txd)
{                        
    unsigned char t;   
	SDA_OUT(); 	    
    IIC_SCL = 0;
    for(t = 0; t < 8; t++)
    {              
        IIC_SDA = (txd & 0x80) >> 7;
        txd <<= 1; 	  
		delay_us(2);   
		IIC_SCL = 1;
		delay_us(2); 
		IIC_SCL = 0;	
		delay_us(2);
    }	 
} 	



unsigned char iic_read_byte(unsigned char ack)
{
	unsigned char i, receive = 0;
	SDA_IN();
    for(i = 0; i < 8; i++)
	{
        IIC_SCL = 0; 
        delay_us(2);
		IIC_SCL = 1;
        receive <<= 1;
        if(READ_SDA) receive++;   
		delay_us(1); 
    }					 
    if (!ack)
        iic_nack();
    else
        iic_ack();   
    return receive;
}


void iic_init()
{	

	gpio_init_type GPIO_InitStructure;
	/* enable the gpioa clock */
	crm_periph_clock_enable(IIC_SCL_PIN_CLOCK, TRUE);
	crm_periph_clock_enable(IIC_SDA_PIN_CLOCK, TRUE);

	/* set default parameter */
	gpio_default_para_init(&GPIO_InitStructure);

	/* configure the gpio */
	GPIO_InitStructure.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
	GPIO_InitStructure.gpio_out_type  = GPIO_OUTPUT_PUSH_PULL;
	GPIO_InitStructure.gpio_mode = GPIO_MODE_OUTPUT;
	GPIO_InitStructure.gpio_pins = IIC_SCL_PIN;
	GPIO_InitStructure.gpio_pull = GPIO_PULL_NONE;
	gpio_init(IIC_SCL_PORT, &GPIO_InitStructure);
	GPIO_InitStructure.gpio_pins = IIC_SDA_PIN;
	gpio_init(IIC_SDA_PORT, &GPIO_InitStructure);

	gpio_bits_set(IIC_SCL_PORT, IIC_SCL_PIN); 
    gpio_bits_set(IIC_SDA_PORT, IIC_SDA_PIN);
}

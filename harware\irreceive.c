#include "irreceive.h"
#include "sys.h"


void set_oc2_polarity(tmr_type *tmrx, tmr_input_polarity_type polarity)
{	
	uint32_t tmpccer = tmrx->cctrl;
	tmpccer &= ~(1 << 5);
	tmpccer |= polarity << 5;
	tmrx->cctrl = tmpccer;
}


void ir_receive_init(void)
{
	gpio_init_type  gpio_init_struct;
	tmr_input_config_type  tmr_input_config_struct;

	crm_periph_clock_enable(IR_PIN_CLOCK, TRUE);
	crm_periph_clock_enable(CRM_TMR2_PERIPH_CLOCK, TRUE);

	/* set default parameter */
	gpio_default_para_init(&gpio_init_struct);

	/* timer3 input pin Configuration */
	gpio_init_struct.gpio_pins = IR_PIN;
	gpio_init_struct.gpio_mode = GPIO_MODE_INPUT;
	gpio_init_struct.gpio_out_type = GPIO_OUTPUT_PUSH_PULL;
	gpio_init_struct.gpio_pull = GPIO_PULL_NONE;
	gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
	gpio_init(IR_PIN_PORT, &gpio_init_struct);

	/* tmr3 counter mode configuration */
	tmr_base_init(TMR2, 10000, SystemCoreClock / 1000000 - 1);
	tmr_cnt_dir_set(TMR2, TMR_COUNT_UP);


	/* configure tmr3 channel2 to get clock signal */
	tmr_input_config_struct.input_filter_value = 0x03;
	tmr_input_config_struct.input_channel_select = TMR_SELECT_CHANNEL_2;
	tmr_input_config_struct.input_mapped_select = TMR_CC_CHANNEL_MAPPED_DIRECT;
	tmr_input_config_struct.input_polarity_select = TMR_INPUT_RISING_EDGE;
	tmr_input_channel_init(TMR2, &tmr_input_config_struct, TMR_CHANNEL_INPUT_DIV_1);

	tmr_interrupt_enable(TMR2, TMR_C2_INT, TRUE);




	nvic_irq_enable(TMR2_GLOBAL_IRQn, 0, 0);


	tmr_flag_clear(TMR2, TMR_OVF_FLAG | TMR_C2_FLAG);
	tmr_interrupt_enable(TMR2, TMR_C2_INT | TMR_OVF_INT, TRUE);
	
	tmr_counter_enable(TMR2, TRUE);

}


						   
static unsigned char rmtSta = 0;	


static unsigned short dVal;	


unsigned char rmtCnt = 0;		  


unsigned int rmtRec = 0;



void TMR2_GLOBAL_IRQHandler(void)
{
	/* enter interrupt */
    rt_interrupt_enter();

	if(tmr_flag_get(TMR2, TMR_OVF_FLAG) != RESET) {
		if (rmtSta & 0x80) {
			rmtSta &= ~0X10;
			if((rmtSta & 0x0F) == 0x00) {
				rmtSta |= 1 << 6;
			}

			if ((rmtSta & 0x0F) < 14) {
				rmtSta++;
			} else {
				rmtSta &= ~(1<<7);
				rmtSta &= 0xF0;
			}			
		}
    	tmr_flag_clear(TMR2, TMR_OVF_FLAG);
  	}


	if (tmr_flag_get(TMR2, TMR_C2_FLAG) != RESET) {
		if(IR_STA) {
			tmr_counter_value_set(TMR2, 0);
			set_oc2_polarity(TMR2, TMR_INPUT_FALLING_EDGE);
			rmtSta |= 0X10;										
		} else {
			dVal = tmr_channel_value_get(TMR2, TMR_SELECT_CHANNEL_2);	
			set_oc2_polarity(TMR2, TMR_INPUT_RISING_EDGE);			
			if(rmtSta & 0X10) {
				if(rmtSta & 0X80) {
					if(dVal > 300 && dVal < 800) {
						rmtRec <<= 1;
					} else if(dVal > 1400 && dVal < 1800) {
						rmtRec <<= 1;
						rmtRec |= 1;
					} else if (dVal > 2200 && dVal < 2600) {
						rmtCnt++;
						rmtSta &= 0xF0;
					}
				} else if(dVal > 4200 && dVal < 4700) {
					rmtSta |= 1<<7;								
					rmtCnt = 0;									
				}						 
			}
			
			rmtSta &= ~(1 << 4);									
		}
        tmr_flag_clear(TMR2, TMR_C2_FLAG);
	}
    
	/* leave interrupt */
    rt_interrupt_leave();
}




unsigned char ir_key_scan(void)
{              
	uint8_t sta = 0;       
    uint8_t t1, t2;  
	if(rmtSta & (1 << 6)) { 
 	    if((rmtRec >> 16) == REMOTE_ID) { 
	        t1 = rmtRec >> 8;
	        t2 = rmtRec; 	
	        if(t1 == (uint8_t)(~t2)) {
				sta = t1;
			}   	
		}  
		if((sta == 0) || ((rmtSta & 0X80) == 0)) {
			
			rmtSta &= ~(1 << 6);		
			rmtCnt = 0;				
		}
		if (sta != 0) {
			rmtSta &= ~(1 << 6);		
			rmtCnt = 0;	
		}
      
	}

    return sta;
}

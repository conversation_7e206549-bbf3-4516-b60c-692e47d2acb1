#ifndef IR_RECEIVE_H_
#define IR_RECEIVE_H_

#include "sys.h"


#define REMOTE_ID       0x2CD5 



#define IR_KEY_UP               0x89
#define IR_KEY_OK               0x59
#define IR_KEY_LEFT             0xE9
#define IR_KEY_DOWN             0x69
#define IR_KEY_RIGHT            0xA9
#define IR_KEY_SET              0xC9
#define IR_KEY_RETURN           0x21
#define IR_NO_KEY               0x00


#define IR_KEY_NUM0             0x79
#define IR_KEY_NUM1             0x49
#define IR_KEY_NUM2             0x01
#define IR_KEY_NUM3             0x41
#define IR_KEY_NUM4             0xE1
#define IR_KEY_NUM5             0x61
#define IR_KEY_NUM6             0xA1
#define IR_KEY_NUM7             0xD1
#define IR_KEY_NUM8             0x51
#define IR_KEY_NUM9             0x91



void ir_receive_init(void);


uint8_t ir_key_scan(void);


void ir_receive_status_clear(void);



#endif





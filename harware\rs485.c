#include "rs485.h"
#include <string.h>
#include "rtthread.h"


#if defined(USE_RS485_1)

static uint8_t rs4851RxBuffer[RS485_1_REV_BUF_SIZE];
static Rs485Handle hrs485_1 = {

                                .timeOutMark = FALSE,
                                .byteTimeOut = 5,
                                .timeOutCount = 6,
                                .rxDataCount = 0,
                                .rxBuf = rs4851RxBuffer,
                                {

                                    .effectSendStatus = HIGH_LEVEL_TRANSMISSION,
                                    .rs_gpio_pins = GPIO_PINS_4,
                                    .rs_gpiox = GPIOA
                                },

                                {
                                    .tx_dma_channel = DMA1_CHANNEL3,
                                    .huart  = USART1,
                                    .tx_dma_complete_flag = DMA1_FDT3_FLAG
                                }};

#endif



#define RS485_WAIT_CLOCK    10
#define rs485_wait_dma()    rt_thread_delay(RS485_WAIT_CLOCK)




void TMR3_GLOBAL_IRQHandler(void)
{
    rt_interrupt_enter();
    if ((TMR3->ists & TMR_OVF_FLAG) != RESET) {
        TMR3->ists = ~TMR_OVF_FLAG;
        if (hrs485_1.timeOutCount < hrs485_1.byteTimeOut) {
            hrs485_1.timeOutCount++;
        } else if (hrs485_1.timeOutCount == hrs485_1.byteTimeOut){
            hrs485_1.timeOutMark = TRUE;
            hrs485_1.timeOutCount++;
        }
    }
    rt_interrupt_leave();
}





static inline void rs485_set_rts(Rs485Handle *hrs485x, uint8_t status)
{
    if (status) {
        gpio_bits_set(hrs485x->rs_gpiox, hrs485x->rs_gpio_pins);
    } else {

        gpio_bits_reset(hrs485x->rs_gpiox, hrs485x->rs_gpio_pins);

    }
}


void uart_monitor_time_init(void)
{

    /* enable tmr3 clock */
    crm_periph_clock_enable(CRM_TMR3_PERIPH_CLOCK, TRUE);
    tmr_base_init(TMR3, 1000-1, (SystemCoreClock/1000000)-1);

    tmr_cnt_dir_set(TMR3, TMR_COUNT_UP);

    /* overflow interrupt enable */
    tmr_interrupt_enable(TMR3, TMR_OVF_INT, TRUE);

    nvic_irq_enable(TMR3_GLOBAL_IRQn, 0, 0);

    /* enable tmr3 */
    tmr_counter_enable(TMR3, TRUE);
}



void set_byte_time_out(Rs485Handle *hrs485x, uint16_t byteTimeOut)
{

    if (byteTimeOut < 0XFFFF) {
        hrs485x->byteTimeOut = byteTimeOut;
        hrs485x->timeOutCount = byteTimeOut + 1;
    }

}


int rs485_read(Rs485Handle *hrs485x, uint8_t* buf, uint16_t length, uint16_t timeOut)
{
    int ret = -1;
    timeOut /= RS485_WAIT_CLOCK;
    while (timeOut-- && hrs485x->timeOutMark == FALSE) {
        rs485_wait_dma();
    }
    if (hrs485x->timeOutMark == FALSE) {
        return 0;
    }

    ret = hrs485x->rxDataCount;
    if (ret > 0) {
        memcpy(buf, hrs485x->rxBuf, ret);
    }
    hrs485x->timeOutMark = FALSE;
    hrs485x->rxDataCount = 0;
    return ret;
}



void rs485_send(Rs485Handle *hrs485x, uint8_t *buf, uint16_t len)
{
    if (hrs485x == NULL) {
        return;
    }
    dma_channel_enable(hrs485x->tx_dma_channel, FALSE);
    hrs485x->tx_dma_channel->dtcnt = len;
    hrs485x->tx_dma_channel->maddr = (uint32_t)buf;

    rs485_set_rts(hrs485x, hrs485x->effectSendStatus == HIGH_LEVEL_TRANSMISSION);
    /*  enable send function  */
    dma_channel_enable(hrs485x->tx_dma_channel, TRUE);
    while ((dma_flag_get(hrs485x->tx_dma_complete_flag) == RESET) && (USART1->sts & USART_TDBE_FLAG) == RESET) {

        rs485_wait_dma();
    }
    /*   dma transfer complete  */
    dma_flag_clear(hrs485x->tx_dma_complete_flag);
    rt_thread_delay(2);
    rs485_set_rts(hrs485x, hrs485x->effectSendStatus != HIGH_LEVEL_TRANSMISSION);
}


static void rs485_dma_configuration(Rs485Handle* hrs485x)
{
    dma_init_type dma_init_struct;
    crm_periph_clock_enable(CRM_DMA1_PERIPH_CLOCK, TRUE);
    dma_flexible_config(DMA1, FLEX_CHANNEL3, DMA_FLEXIBLE_UART1_TX);

    dma_reset(hrs485x->tx_dma_channel);

    dma_default_para_init(&dma_init_struct);
    dma_init_struct.buffer_size = 0;
    dma_init_struct.direction = DMA_DIR_MEMORY_TO_PERIPHERAL;
    dma_init_struct.memory_base_addr = (uint32_t)0;
    dma_init_struct.memory_data_width = DMA_MEMORY_DATA_WIDTH_BYTE;
    dma_init_struct.memory_inc_enable = TRUE;
    dma_init_struct.peripheral_base_addr = (uint32_t)&hrs485x->huart->dt;
    dma_init_struct.peripheral_data_width = DMA_PERIPHERAL_DATA_WIDTH_BYTE;
    dma_init_struct.peripheral_inc_enable = FALSE;
    dma_init_struct.priority = DMA_PRIORITY_MEDIUM;
    dma_init_struct.loop_mode_enable = FALSE;

    dma_init(hrs485x->tx_dma_channel, &dma_init_struct);
    dma_channel_enable(hrs485x->tx_dma_channel, FALSE);
}




Rs485Handle *rs485_init(Rs485ProtocolConfig_t *config, uint8_t effectSendBit)
{

    gpio_init_type gpio_init_struct;
    Rs485Handle *hrs485x;

    gpio_default_para_init(&gpio_init_struct);
    crm_periph_clock_enable(CRM_USART1_PERIPH_CLOCK, TRUE);
    crm_periph_clock_enable(CRM_GPIOA_PERIPH_CLOCK, TRUE);
//    usart_enable(USART1, FALSE);
//    usart_interrupt_enable(USART1, USART_RDBF_INT, FALSE);


    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
    gpio_init_struct.gpio_out_type  = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_MUX;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;

    gpio_init_struct.gpio_pins = GPIO_PINS_9;
    gpio_init(GPIOA, &gpio_init_struct);

    gpio_init_struct.gpio_mode = GPIO_MODE_INPUT;
    gpio_init_struct.gpio_pins = GPIO_PINS_10;
    gpio_init_struct.gpio_pull = GPIO_PULL_UP;
    gpio_init(GPIOA, &gpio_init_struct);

    if (config == NULL) {

        usart_init(USART1, 9600, USART_DATA_8BITS, USART_STOP_1_BIT);

    } else {

        usart_init(USART1, config->baudRate, USART_DATA_8BITS, config->stopBits);

        usart_parity_selection_config(USART1, config->parityType);
    }

    usart_transmitter_enable(USART1, TRUE);
    usart_receiver_enable(USART1, TRUE);
    usart_dma_transmitter_enable(USART1, TRUE);
    nvic_irq_enable(USART1_IRQn, 0, 0);
    usart_interrupt_enable(USART1, USART_RDBF_INT, TRUE);
    usart_enable(USART1, TRUE);
    hrs485x = &hrs485_1;

    rs485_dma_configuration(hrs485x);

    /*  config rts pin  */
    if (hrs485x != NULL) {

        hrs485x->effectSendStatus = effectSendBit;
        gpio_default_para_init(&gpio_init_struct);
        gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
        gpio_init_struct.gpio_out_type  = GPIO_OUTPUT_PUSH_PULL;
        gpio_init_struct.gpio_mode = GPIO_MODE_OUTPUT;
        gpio_init_struct.gpio_pins = hrs485x->rs_gpio_pins;
        if (effectSendBit == HIGH_LEVEL_TRANSMISSION) {

            gpio_init_struct.gpio_pull = GPIO_PULL_DOWN;
        } else {

            gpio_init_struct.gpio_pull = GPIO_PULL_UP;
        }
        gpio_init(hrs485x->rs_gpiox, &gpio_init_struct);
        rs485_set_rts(hrs485x, hrs485x->effectSendStatus != HIGH_LEVEL_TRANSMISSION);
    }
    return hrs485x;
}





#if defined (USE_RS485_1)
void USART1_IRQHandler(void)
{
    rt_interrupt_enter();
    if ((USART1->sts&USART_RDBF_FLAG)) {

        uint8_t ch = USART1->dt;
        if (hrs485_1.rxDataCount < RS485_1_REV_BUF_SIZE) {
            hrs485_1.rxBuf[hrs485_1.rxDataCount++] = ch;
        }
        hrs485_1.timeOutCount = 0;
    }
    rt_interrupt_leave();
}
#endif

#ifndef __RS485_H
#define __RS485_H



#include "at32f413.h"


typedef struct {
    
    uint32_t baudRate;
    usart_stop_bit_num_type stopBits;
    usart_parity_selection_type parityType;   
} Rs485ProtocolConfig_t;


typedef struct {
    
    
    
    uint8_t  timeOutMark;
    uint16_t byteTimeOut;
    uint16_t timeOutCount;
    
    uint16_t rxDataCount;    
    uint8_t* rxBuf;
   

    struct {
                
        uint8_t effectSendStatus;
        uint16_t    rs_gpio_pins;
        gpio_type*  rs_gpiox;        
    };
    struct {
        
        dma_channel_type* tx_dma_channel;
        usart_type* huart;
        uint32_t tx_dma_complete_flag;
    };
} Rs485Handle;


/*
    resource usage
hardtimer3  TMR3
uart1 : PA9--TX     PA10--RX   DMA2CH1--TX   PE14
uart3 : PC10--TX    PC11--RX   DMA1CH1--TX   PE15
uart7 : PA4--TX     PA5--RX    DMA1CH2--TX   
*/

#define USE_RS485_1


#define RS485_1_REV_BUF_SIZE   256


#define HIGH_LEVEL_TRANSMISSION     1
#define LOW_LEVEL_TRANSMISSION      0


#define WAIT_RS485_FOREVER       0XFFFF

void uart_monitor_time_init(void);
void set_byte_time_out(Rs485Handle *hrs485x, uint16_t byteTimeOut);
Rs485Handle *rs485_init(Rs485ProtocolConfig_t *config, uint8_t effectSendBit);
int rs485_read(Rs485Handle *hrs485x, uint8_t *buf, uint16_t length, uint16_t timeOut);
void rs485_send(Rs485Handle *hrs485x, uint8_t *buf, uint16_t len);
#endif







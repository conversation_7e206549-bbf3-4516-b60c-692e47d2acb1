#include "usart.h"
#include <stdio.h>
#include <string.h>


#define UART_RX_SIZE            32
static uint8_t uart_rx_buf[UART_RX_SIZE];
static uint16_t uart_rx_len;


void uart_init()
{
    gpio_init_type gpio_init_struct;
    dma_init_type dma_init_struct; 

    /* enable the usart1 and gpio clock */
    crm_periph_clock_enable(CRM_USART2_PERIPH_CLOCK, TRUE);  
    crm_periph_clock_enable(CRM_GPIOA_PERIPH_CLOCK, TRUE);


    gpio_default_para_init(&gpio_init_struct);
    /* configure the usart2 tx pin */
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
    gpio_init_struct.gpio_out_type  = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_MUX;
    gpio_init_struct.gpio_pins = GPIO_PINS_2;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;
    gpio_init(GPIOA, &gpio_init_struct);

    gpio_init_struct.gpio_mode = GPIO_MODE_INPUT;
    gpio_init_struct.gpio_pins = GPIO_PINS_3;
    gpio_init_struct.gpio_pull = GPIO_PULL_UP;
    gpio_init(GPIOA, &gpio_init_struct);

    /* configure usart1 param */
    usart_init(USART2, 115200, USART_DATA_8BITS, USART_STOP_1_BIT);

    usart_transmitter_enable(USART2, TRUE);
    usart_receiver_enable(USART2, TRUE);
    usart_dma_transmitter_enable(USART2, TRUE);
    usart_dma_receiver_enable(USART2, TRUE);
    usart_enable(USART2, TRUE);

    /* enable dma1 clock */
    crm_periph_clock_enable(CRM_DMA1_PERIPH_CLOCK, TRUE);  

    /* dma1 channel1 for usart1 tx configuration */
    dma_reset(DMA1_CHANNEL1);
    dma_default_para_init(&dma_init_struct);  
    dma_init_struct.buffer_size = 0;
    dma_init_struct.direction = DMA_DIR_MEMORY_TO_PERIPHERAL;
    dma_init_struct.memory_base_addr = (uint32_t)0;
    dma_init_struct.memory_data_width = DMA_MEMORY_DATA_WIDTH_BYTE;
    dma_init_struct.memory_inc_enable = TRUE;
    dma_init_struct.peripheral_base_addr = (uint32_t)&USART2->dt;
    dma_init_struct.peripheral_data_width = DMA_PERIPHERAL_DATA_WIDTH_BYTE;
    dma_init_struct.peripheral_inc_enable = FALSE;
    dma_init_struct.priority = DMA_PRIORITY_MEDIUM;
    dma_init_struct.loop_mode_enable = FALSE;
    dma_init(DMA1_CHANNEL1, &dma_init_struct);

    /* config flexible dma for usart2 tx */
    dma_flexible_config(DMA1, FLEX_CHANNEL1, DMA_FLEXIBLE_UART2_TX);

    /* dma1 channel2 for usart2 rx configuration */
    dma_reset(DMA1_CHANNEL2);
    dma_default_para_init(&dma_init_struct);  
    dma_init_struct.buffer_size = UART_RX_SIZE;
    dma_init_struct.direction = DMA_DIR_PERIPHERAL_TO_MEMORY;
    dma_init_struct.memory_base_addr = (uint32_t)uart_rx_buf;
    dma_init_struct.memory_data_width = DMA_MEMORY_DATA_WIDTH_BYTE;
    dma_init_struct.memory_inc_enable = TRUE;
    dma_init_struct.peripheral_base_addr = (uint32_t)&USART2->dt;
    dma_init_struct.peripheral_data_width = DMA_PERIPHERAL_DATA_WIDTH_BYTE;
    dma_init_struct.peripheral_inc_enable = FALSE;
    dma_init_struct.priority = DMA_PRIORITY_MEDIUM;
    dma_init_struct.loop_mode_enable = FALSE;
    dma_init(DMA1_CHANNEL2, &dma_init_struct);

    /* config flexible dma for usart3 rx */
    dma_flexible_config(DMA1, FLEX_CHANNEL2, DMA_FLEXIBLE_UART2_RX);

    /*  intterrupt  */
    nvic_irq_enable(USART2_IRQn, 0, 0);
    usart_interrupt_enable(USART2, USART_IDLE_INT, TRUE);
    nvic_irq_enable(DMA1_Channel2_IRQn, 0, 0);
    // dma_interrupt_enable(DMA1_CHANNEL2, DMA_FDT_INT, TRUE);

    dma_channel_enable(DMA1_CHANNEL2, TRUE); 
    dma_channel_enable(DMA1_CHANNEL1, TRUE); 
}




void uart_send(const void *buf, uint16_t len)
{
	DMA1_CHANNEL1->ctrl_bit.chen = FALSE;
    DMA1_CHANNEL1->dtcnt = len;
    DMA1_CHANNEL1->maddr = (uint32_t)buf;
    DMA1_CHANNEL1->ctrl_bit.chen = TRUE;
    while ((DMA1->sts & DMA1_FDT1_FLAG) == RESET && (USART2->sts & USART_TDBE_FLAG) == RESET) {
        rt_thread_delay(1);
    }
    DMA1->clr = DMA1_FDT1_FLAG;
}


int uart_read(void *buf, uint16_t len, uint32_t timeout)
{
    timeout /= 10;
    while (timeout-- && uart_rx_len < len) {
        rt_thread_delay(10);
    }
    int rc = len > uart_rx_len ? uart_rx_len : len;
    if (rc > 0) {
        memcpy(buf, uart_rx_buf, rc);
    }
    uart_rx_len = 0;
    return rc;
}


 void USART2_IRQHandler()
 {
     rt_interrupt_enter();
     if (USART2->sts & USART_IDLEF_FLAG) {
        (void)USART2->dt;
        uart_rx_len = UART_RX_SIZE - DMA1_CHANNEL2->dtcnt;
        DMA1_CHANNEL2->ctrl_bit.chen = FALSE;
        DMA1_CHANNEL2->dtcnt = UART_RX_SIZE;
        DMA1_CHANNEL2->ctrl_bit.chen = TRUE;
     }
     rt_interrupt_leave();
 }

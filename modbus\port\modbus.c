#include "modbus.h"
#include <string.h>


uint16_t tab_registers[16] = {0};

static int get_registers_buf(void *buf, int bufsz)
{
    uint16_t *ptr = (uint16_t *)buf;
    uint16_t *reg = tab_registers;

    for (int i = 0; i < 8; i++) {
        ptr[i] = reg[i];
    }
    return 0;
}


static int set_registers_buf(int index, int len, void *buf, int bufsz)
{
    uint16_t *ptr = (uint16_t *)buf;
    uint16_t *reg = tab_registers;
    for (int i = 0; i < len; i++) {
        reg[index + i] = ptr[index + i];
    }
    return 0;
}


const agile_modbus_slave_util_map_t register_maps[] = {
    {0, 8, get_registers_buf, set_registers_buf}
};



static int addr_check(agile_modbus_t *ctx, struct agile_modbus_slave_info *slave_info)
{
    int slave = slave_info->sft->slave;
    if ((slave != ctx->slave) && (slave != AGILE_MODBUS_BROADCAST_ADDRESS) && (slave != 0xFF))
        return -AGILE_MODBUS_EXCEPTION_UNKNOW;
    
    return 0;
}


const agile_modbus_slave_util_t slave_util = {
    NULL,
    0,
    NULL,
    0,
    register_maps,
    sizeof(register_maps) / sizeof(register_maps[0]),
    NULL,
    0,
    addr_check,
    NULL,
    NULL};



static modbus_map_t mbSlaveMapping = {.holdingRegister = tab_registers,
                            .inputRegister = NULL,
                            .coilRegister = NULL,
                            .discreteRegister = NULL};

modbus_map_t *get_slave_mapping(void)
{
    modbus_map_t *mb_map;
    mb_map = &mbSlaveMapping;

    return mb_map;
}

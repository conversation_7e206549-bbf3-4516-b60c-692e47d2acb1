/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-08-11 08:19:46
 * @LastEditTime: 2021-08-26 09:40:26
 * @LastEditors: your name
 */
#ifndef __MODBUS_H_
#define __MODBUS_H_


#include "at32f413.h"
#include "agile_modbus.h"
#include "agile_modbus_slave_util.h"
#include "rs485.h"


typedef struct {
    
    unsigned short *holdingRegister;
    unsigned short *inputRegister;
    unsigned char *discreteRegister;
    unsigned char *coilRegister;
} modbus_map_t;


extern const agile_modbus_slave_util_t slave_util;

modbus_map_t *get_slave_mapping(void);



int modbus_read_input_bits(Rs485Handle* hrs485x, agile_modbus_t *ctx, int addr, int nb, uint8_t* buf, uint16_t timeout);
int modbus_read_input_registers(Rs485Handle* hrs485x, agile_modbus_t *ctx, int addr, 
                        int nb, uint16_t* buf, uint16_t timeout);
int modbus_write_bit(Rs485Handle* hrs485x, agile_modbus_t *ctx, int addr, int status, uint16_t timeout);
int modbus_read_registers(Rs485Handle* hrs485x, agile_modbus_t *ctx, int addr, int nb, uint16_t* buf, uint16_t timeout);
#endif



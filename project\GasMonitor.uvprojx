<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Debug</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>-AT32F413C8T7</Device>
          <Vendor>ArteryTek</Vendor>
          <PackID>ArteryTek.AT32F413_DFP.2.1.7</PackID>
          <Cpu>IRAM(0x20000000,0x8000) IROM(0x08000000,0x10000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0AT32F413_64 -********** -FL010000 -FP0($$Device:-AT32F413C8T7$Flash\AT32F413_64.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:-AT32F413C8T7$Device\Include\at32f413.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:-AT32F413C8T7$SVD\AT32F413xx_v2.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\obj\</OutputDirectory>
          <OutputName>GasMonitor</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>$KARM\ARMCLANG\bin\fromelf.exe --bin --output=@L.bin !L</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>6</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>AT32F413C8T7,USE_STDPERIPH_DRIVER</Define>
              <Undefine></Undefine>
              <IncludePath>..\app;..\libraries\drivers\inc;..\libraries\cmsis\cm4\core_support;..\libraries\cmsis\cm4\device_support;..\rt-thread\bsp;..\rt-thread\include;..\rt-thread\include\libc;..\modbus;..\modbus\port;..\harware</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>user</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\main.c</FilePath>
            </File>
            <File>
              <FileName>sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\sys.c</FilePath>
            </File>
            <File>
              <FileName>menu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\menu.c</FilePath>
            </File>
            <File>
              <FileName>protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\protocol.c</FilePath>
            </File>
            <File>
              <FileName>mb_slave.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\mb_slave.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>core</GroupName>
          <Files>
            <File>
              <FileName>system_at32f413.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\cmsis\cm4\device_support\system_at32f413.c</FilePath>
            </File>
            <File>
              <FileName>startup_at32f413.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\libraries\cmsis\cm4\device_support\startup\mdk\startup_at32f413.s</FilePath>
            </File>
            <File>
              <FileName>at32f413_clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\at32f413_clock.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_int.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\at32f413_int.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>libraries</GroupName>
          <Files>
            <File>
              <FileName>at32f413_tmr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_tmr.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_spi.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_usart.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_crm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_crm.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_dma.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_flash.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_gpio.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_i2c.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_misc.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_pwc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_pwc.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_exint.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_exint.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_rtc.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_bpr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_bpr.c</FilePath>
            </File>
            <File>
              <FileName>at32f413_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\drivers\src\at32f413_wdt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>hardware</GroupName>
          <Files>
            <File>
              <FileName>rs485.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\harware\rs485.c</FilePath>
            </File>
            <File>
              <FileName>ch455.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\harware\ch455.c</FilePath>
            </File>
            <File>
              <FileName>irreceive.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\harware\irreceive.c</FilePath>
            </File>
            <File>
              <FileName>current.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\harware\current.c</FilePath>
            </File>
            <File>
              <FileName>iic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\harware\iic.c</FilePath>
            </File>
            <File>
              <FileName>flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\harware\flash.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\harware\gpio.c</FilePath>
            </File>
            <File>
              <FileName>rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\harware\rtc.c</FilePath>
            </File>
            <File>
              <FileName>usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\harware\usart.c</FilePath>
            </File>
            <File>
              <FileName>wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\harware\wdt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>rtthread/port</GroupName>
          <Files>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\bsp\board.c</FilePath>
            </File>
            <File>
              <FileName>rtconfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\rt-thread\bsp\rtconfig.h</FilePath>
            </File>
            <File>
              <FileName>cpuport.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\libcpu\arm\cortex-m4\cpuport.c</FilePath>
            </File>
            <File>
              <FileName>context_rvds.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\rt-thread\libcpu\arm\cortex-m4\context_rvds.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>rtthread/src</GroupName>
          <Files>
            <File>
              <FileName>clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\clock.c</FilePath>
            </File>
            <File>
              <FileName>components.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\components.c</FilePath>
            </File>
            <File>
              <FileName>cpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\cpu.c</FilePath>
            </File>
            <File>
              <FileName>idle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\idle.c</FilePath>
            </File>
            <File>
              <FileName>ipc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\ipc.c</FilePath>
            </File>
            <File>
              <FileName>irq.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\irq.c</FilePath>
            </File>
            <File>
              <FileName>kservice.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\kservice.c</FilePath>
            </File>
            <File>
              <FileName>mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\mem.c</FilePath>
            </File>
            <File>
              <FileName>memheap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\memheap.c</FilePath>
            </File>
            <File>
              <FileName>mempool.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\mempool.c</FilePath>
            </File>
            <File>
              <FileName>object.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\object.c</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>slab.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\slab.c</FilePath>
            </File>
            <File>
              <FileName>thread.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\thread.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rt-thread\src\timer.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>mb</GroupName>
          <Files>
            <File>
              <FileName>agile_modbus.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\modbus\agile_modbus.c</FilePath>
            </File>
            <File>
              <FileName>agile_modbus_rtu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\modbus\agile_modbus_rtu.c</FilePath>
            </File>
            <File>
              <FileName>agile_modbus_slave_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\modbus\port\agile_modbus_slave_util.c</FilePath>
            </File>
            <File>
              <FileName>modbus.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\modbus\port\modbus.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>GasMonitor</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
